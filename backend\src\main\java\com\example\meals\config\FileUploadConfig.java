package com.example.meals.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 文件上传配置
 */
@Configuration
@ConfigurationProperties(prefix = "file.upload")
public class FileUploadConfig {
    
    // 上传文件存储路径
    private String path = "uploads/";
    
    // 头像文件存储路径
    private String avatarPath = "uploads/avatars/";
    
    // 允许的文件类型
    private String[] allowedTypes = {"jpg", "jpeg", "png", "gif"};
    
    // 最大文件大小（字节）
    private long maxSize = 2 * 1024 * 1024; // 2MB
    
    // 头像最大尺寸
    private int avatarMaxWidth = 500;
    private int avatarMaxHeight = 500;
    
    // 缩略图尺寸
    private int thumbnailWidth = 100;
    private int thumbnailHeight = 100;
    
    // Getter 和 Setter 方法
    public String getPath() {
        return path;
    }
    
    public void setPath(String path) {
        this.path = path;
    }
    
    public String getAvatarPath() {
        return avatarPath;
    }
    
    public void setAvatarPath(String avatarPath) {
        this.avatarPath = avatarPath;
    }
    
    public String[] getAllowedTypes() {
        return allowedTypes;
    }
    
    public void setAllowedTypes(String[] allowedTypes) {
        this.allowedTypes = allowedTypes;
    }
    
    public long getMaxSize() {
        return maxSize;
    }
    
    public void setMaxSize(long maxSize) {
        this.maxSize = maxSize;
    }
    
    public int getAvatarMaxWidth() {
        return avatarMaxWidth;
    }
    
    public void setAvatarMaxWidth(int avatarMaxWidth) {
        this.avatarMaxWidth = avatarMaxWidth;
    }
    
    public int getAvatarMaxHeight() {
        return avatarMaxHeight;
    }
    
    public void setAvatarMaxHeight(int avatarMaxHeight) {
        this.avatarMaxHeight = avatarMaxHeight;
    }
    
    public int getThumbnailWidth() {
        return thumbnailWidth;
    }
    
    public void setThumbnailWidth(int thumbnailWidth) {
        this.thumbnailWidth = thumbnailWidth;
    }
    
    public int getThumbnailHeight() {
        return thumbnailHeight;
    }
    
    public void setThumbnailHeight(int thumbnailHeight) {
        this.thumbnailHeight = thumbnailHeight;
    }
}
