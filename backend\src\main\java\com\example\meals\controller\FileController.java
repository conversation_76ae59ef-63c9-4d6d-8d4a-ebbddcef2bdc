package com.example.meals.controller;

import com.example.meals.common.Result;
import com.example.meals.service.FileService;
import com.example.meals.service.UserService;
import com.example.meals.service.AdminService;
import com.example.meals.utils.AuthUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletRequest;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 文件上传控制器
 */
@RestController
@RequestMapping("/api/files")
public class FileController {
    
    @Autowired
    private FileService fileService;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private AdminService adminService;
    
    /**
     * 上传用户头像
     */
    @PostMapping("/avatar/user")
    public Result<String> uploadUserAvatar(@RequestParam("file") MultipartFile file, 
                                         HttpServletRequest request) {
        // 验证用户权限
        Long userId = AuthUtil.getCurrentUserId(request);
        if (userId == null) {
            return Result.unauthorized("用户未登录");
        }
        String userType = (String) request.getAttribute("userType");
        
        if (!"USER".equals(userType)) {
            return Result.forbidden("权限不足，仅普通用户可访问");
        }
        
        // 上传文件
        Result<String> uploadResult = fileService.uploadAvatar(file, userId, "USER");
        if (!uploadResult.getSuccess()) {
            return uploadResult;
        }
        
        // 更新用户头像路径
        Result<Void> updateResult = userService.updateUserAvatar(userId, uploadResult.getData());
        if (!updateResult.getSuccess()) {
            // 如果更新失败，删除已上传的文件
            fileService.deleteAvatar(uploadResult.getData());
            return Result.error("头像更新失败：" + updateResult.getMessage());
        }
        
        return Result.success("头像上传成功", uploadResult.getData());
    }
    
    /**
     * 上传管理员头像
     */
    @PostMapping("/avatar/admin")
    public Result<String> uploadAdminAvatar(@RequestParam("file") MultipartFile file, 
                                          HttpServletRequest request) {
        // 验证管理员权限
        Long adminId = AuthUtil.getCurrentUserId(request);
        if (adminId == null) {
            return Result.unauthorized("用户未登录");
        }
        String userType = (String) request.getAttribute("userType");
        
        if (!"ADMIN".equals(userType)) {
            return Result.forbidden("权限不足，仅管理员可访问");
        }
        
        // 上传文件
        Result<String> uploadResult = fileService.uploadAvatar(file, adminId, "ADMIN");
        if (!uploadResult.getSuccess()) {
            return uploadResult;
        }
        
        // 更新管理员头像路径
        Result<Void> updateResult = adminService.updateAdminAvatar(adminId, uploadResult.getData());
        if (!updateResult.getSuccess()) {
            // 如果更新失败，删除已上传的文件
            fileService.deleteAvatar(uploadResult.getData());
            return Result.error("头像更新失败：" + updateResult.getMessage());
        }
        
        return Result.success("头像上传成功", uploadResult.getData());
    }
    
    /**
     * 获取文件资源
     */
    @GetMapping("/uploads/**")
    public ResponseEntity<Resource> getFile(HttpServletRequest request) {
        try {
            // 获取文件路径
            String requestURI = request.getRequestURI();
            String filePath = requestURI.substring("/api/files/".length());
            
            Path file = Paths.get(filePath);
            Resource resource = new UrlResource(file.toUri());
            
            if (resource.exists() && resource.isReadable()) {
                // 确定文件类型
                String contentType = "application/octet-stream";
                String filename = file.getFileName().toString().toLowerCase();
                
                if (filename.endsWith(".jpg") || filename.endsWith(".jpeg")) {
                    contentType = "image/jpeg";
                } else if (filename.endsWith(".png")) {
                    contentType = "image/png";
                } else if (filename.endsWith(".gif")) {
                    contentType = "image/gif";
                }
                
                return ResponseEntity.ok()
                        .contentType(MediaType.parseMediaType(contentType))
                        .header(HttpHeaders.CACHE_CONTROL, "max-age=3600")
                        .body(resource);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            return ResponseEntity.notFound().build();
        }
    }
    
    /**
     * 删除头像
     */
    @DeleteMapping("/avatar")
    public Result<Void> deleteAvatar(HttpServletRequest request) {
        // 验证用户权限
        Long userId = AuthUtil.getCurrentUserId(request);
        if (userId == null) {
            return Result.unauthorized("用户未登录");
        }
        String userType = (String) request.getAttribute("userType");
        
        if ("USER".equals(userType)) {
            return userService.deleteUserAvatar(userId);
        } else if ("ADMIN".equals(userType)) {
            return adminService.deleteAdminAvatar(userId);
        } else {
            return Result.forbidden("权限不足");
        }
    }
}
