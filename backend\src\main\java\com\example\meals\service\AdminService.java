package com.example.meals.service;

import com.example.meals.dto.*;
import com.example.meals.common.Result;

/**
 * 管理员服务接口
 */
public interface AdminService {
    
    /**
     * 管理员登录
     */
    Result<AdminResponse> login(AdminLoginRequest request);



    /**
     * 管理员邮箱验证码登录
     */
    Result<AdminResponse> loginWithEmailCode(AdminEmailCodeLoginRequest request);
    

    
    /**
     * 删除管理员
     */
    Result<Void> deleteAdmin(Long id);
    
    /**
     * 根据ID获取管理员信息
     */
    Result<AdminResponse> getAdminById(Long id);
    
    /**
     * 分页查询管理员列表
     */
    Result<PageResponse<AdminResponse>> getAdminList(Integer page, Integer size,
                                                     String username, String realName,
                                                     Integer status);
    
    /**
     * 启用/禁用管理员
     */
    Result<Void> toggleAdminStatus(Long id, Integer status);
    
    /**
     * 重置管理员密码
     */
    Result<Void> resetPassword(Long id, String newPassword);

    /**
     * 更新管理员头像
     */
    Result<Void> updateAdminAvatar(Long adminId, String avatarPath);

    /**
     * 删除管理员头像
     */
    Result<Void> deleteAdminAvatar(Long adminId);


    // ==================== 用户管理功能 ====================

    /**
     * 分页查询用户列表（管理员功能）
     */
    Result<PageResponse<UserResponse>> getUserList(Integer page, Integer size,
                                                   String username, String email,
                                                   Integer status);

    /**
     * 启用/禁用用户
     */
    Result<Void> toggleUserStatus(Long id, Integer status);

    /**
     * 删除用户（管理员功能）
     */
    Result<Void> deleteUser(Long id);

    /**
     * 获取用户统计信息
     */
    Result<UserStatsResponse> getUserStats();
}
