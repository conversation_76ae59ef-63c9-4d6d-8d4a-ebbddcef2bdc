package com.example.meals.service;

import com.example.meals.dto.LoginRequest;
import com.example.meals.dto.EmailCodeLoginRequest;
import com.example.meals.dto.RegisterRequest;
import com.example.meals.dto.UserResponse;
import com.example.meals.dto.request.ResetPasswordRequest;
import com.example.meals.common.Result;

/**
 * 用户服务接口
 */
public interface UserService {
    
    /**
     * 用户注册
     */
    Result<UserResponse> register(RegisterRequest request);
    
    /**
     * 用户登录
     */
    Result<UserResponse> login(LoginRequest request);

    /**
     * 邮箱验证码登录
     */
    Result<UserResponse> loginWithEmailCode(EmailCodeLoginRequest request);
    
    /**
     * 检查用户名是否可用
     */
    Result<Boolean> checkUsername(String username);
    
    /**
     * 检查邮箱是否可用
     */
    Result<Boolean> checkEmail(String email);
    
    /**
     * 根据ID获取用户信息
     */
    Result<UserResponse> getUserById(Long id);

    /**
     * 更新用户头像
     */
    Result<Void> updateUserAvatar(Long userId, String avatarPath);

    /**
     * 删除用户头像
     */
    Result<Void> deleteUserAvatar(Long userId);

    /**
     * 重置密码
     */
    Result<Void> resetPassword(ResetPasswordRequest request);
}
