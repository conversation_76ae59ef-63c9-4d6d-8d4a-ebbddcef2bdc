package com.example.meals.service.impl;

import com.example.meals.common.Result;
import com.example.meals.config.FileUploadConfig;
import com.example.meals.service.FileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.UUID;

/**
 * 文件服务实现类
 */
@Service
public class FileServiceImpl implements FileService {
    
    @Autowired
    private FileUploadConfig fileUploadConfig;
    
    @Override
    public Result<String> uploadAvatar(MultipartFile file, Long userId, String userType) {
        // 验证文件
        Result<Void> validateResult = validateImageFile(file);
        if (!validateResult.getSuccess()) {
            return Result.error(validateResult.getMessage());
        }
        
        try {
            // 创建目录
            String avatarPath = fileUploadConfig.getAvatarPath();
            Path uploadPath = Paths.get(avatarPath);
            if (!Files.exists(uploadPath)) {
                Files.createDirectories(uploadPath);
            }
            
            // 生成文件名
            String originalFilename = file.getOriginalFilename();
            String extension = getFileExtension(originalFilename);
            String filename = generateFilename(userId, userType, extension);
            
            // 保存原始文件
            Path filePath = uploadPath.resolve(filename);
            file.transferTo(filePath.toFile());
            
            // 处理图片（压缩和生成缩略图）
            processImage(filePath.toFile(), extension);
            
            // 返回相对路径
            String relativePath = avatarPath + filename;
            return Result.success("头像上传成功", relativePath);
            
        } catch (IOException e) {
            return Result.error("文件上传失败：" + e.getMessage());
        } catch (Exception e) {
            return Result.error("头像处理失败：" + e.getMessage());
        }
    }
    
    @Override
    public Result<Void> deleteAvatar(String filePath) {
        if (!StringUtils.hasText(filePath)) {
            return Result.success();
        }
        
        try {
            Path path = Paths.get(filePath);
            if (Files.exists(path)) {
                Files.delete(path);
                
                // 同时删除缩略图
                String thumbnailPath = getThumbnailPath(filePath);
                Path thumbPath = Paths.get(thumbnailPath);
                if (Files.exists(thumbPath)) {
                    Files.delete(thumbPath);
                }
            }
            return Result.success();
        } catch (IOException e) {
            return Result.error("删除文件失败：" + e.getMessage());
        }
    }
    
    @Override
    public String getFileUrl(String filePath) {
        if (!StringUtils.hasText(filePath)) {
            return null;
        }
        // 这里可以根据需要返回完整的URL，目前返回相对路径
        return "/" + filePath;
    }
    
    @Override
    public Result<Void> validateImageFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return Result.badRequest("请选择要上传的文件");
        }
        
        // 检查文件大小
        if (file.getSize() > fileUploadConfig.getMaxSize()) {
            return Result.badRequest("文件大小不能超过 " + (fileUploadConfig.getMaxSize() / 1024 / 1024) + "MB");
        }
        
        // 检查文件类型
        String originalFilename = file.getOriginalFilename();
        if (!StringUtils.hasText(originalFilename)) {
            return Result.badRequest("文件名不能为空");
        }
        
        String extension = getFileExtension(originalFilename).toLowerCase();
        boolean isAllowedType = Arrays.stream(fileUploadConfig.getAllowedTypes())
                .anyMatch(type -> type.equalsIgnoreCase(extension));
        
        if (!isAllowedType) {
            return Result.badRequest("只支持以下格式的图片：" + String.join(", ", fileUploadConfig.getAllowedTypes()));
        }
        
        // 验证是否为有效的图片文件
        try {
            BufferedImage image = ImageIO.read(file.getInputStream());
            if (image == null) {
                return Result.badRequest("文件不是有效的图片格式");
            }
        } catch (IOException e) {
            return Result.badRequest("文件读取失败，请确保文件完整");
        }
        
        return Result.success();
    }
    
    @Override
    public Result<Integer> cleanTempFiles() {
        // 清理临时文件的逻辑，这里暂时返回成功
        return Result.success("清理完成", 0);
    }
    
    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (!StringUtils.hasText(filename)) {
            return "";
        }
        int lastDotIndex = filename.lastIndexOf('.');
        return lastDotIndex > 0 ? filename.substring(lastDotIndex + 1) : "";
    }
    
    /**
     * 生成文件名
     */
    private String generateFilename(Long userId, String userType, String extension) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String uuid = UUID.randomUUID().toString().replace("-", "").substring(0, 8);
        return String.format("%s_%d_%s_%s.%s", userType.toLowerCase(), userId, timestamp, uuid, extension);
    }
    
    /**
     * 处理图片（压缩和生成缩略图）
     */
    private void processImage(File imageFile, String extension) throws IOException {
        BufferedImage originalImage = ImageIO.read(imageFile);
        if (originalImage == null) {
            return;
        }
        
        // 压缩原图（如果尺寸过大）
        int maxWidth = fileUploadConfig.getAvatarMaxWidth();
        int maxHeight = fileUploadConfig.getAvatarMaxHeight();
        
        if (originalImage.getWidth() > maxWidth || originalImage.getHeight() > maxHeight) {
            BufferedImage resizedImage = resizeImage(originalImage, maxWidth, maxHeight);
            ImageIO.write(resizedImage, extension, imageFile);
        }
        
        // 生成缩略图
        generateThumbnail(originalImage, imageFile.getAbsolutePath(), extension);
    }
    
    /**
     * 调整图片尺寸
     */
    private BufferedImage resizeImage(BufferedImage originalImage, int maxWidth, int maxHeight) {
        int originalWidth = originalImage.getWidth();
        int originalHeight = originalImage.getHeight();
        
        // 计算缩放比例
        double scaleX = (double) maxWidth / originalWidth;
        double scaleY = (double) maxHeight / originalHeight;
        double scale = Math.min(scaleX, scaleY);
        
        int newWidth = (int) (originalWidth * scale);
        int newHeight = (int) (originalHeight * scale);
        
        BufferedImage resizedImage = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = resizedImage.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g2d.drawImage(originalImage, 0, 0, newWidth, newHeight, null);
        g2d.dispose();
        
        return resizedImage;
    }
    
    /**
     * 生成缩略图
     */
    private void generateThumbnail(BufferedImage originalImage, String originalPath, String extension) throws IOException {
        int thumbWidth = fileUploadConfig.getThumbnailWidth();
        int thumbHeight = fileUploadConfig.getThumbnailHeight();
        
        BufferedImage thumbnail = resizeImage(originalImage, thumbWidth, thumbHeight);
        
        String thumbnailPath = getThumbnailPath(originalPath);
        File thumbnailFile = new File(thumbnailPath);
        ImageIO.write(thumbnail, extension, thumbnailFile);
    }
    
    /**
     * 获取缩略图路径
     */
    private String getThumbnailPath(String originalPath) {
        int lastDotIndex = originalPath.lastIndexOf('.');
        if (lastDotIndex > 0) {
            return originalPath.substring(0, lastDotIndex) + "_thumb" + originalPath.substring(lastDotIndex);
        }
        return originalPath + "_thumb";
    }
}
