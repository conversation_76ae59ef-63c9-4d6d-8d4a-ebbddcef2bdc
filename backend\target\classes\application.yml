# Spring Boot 应用配置
spring:
  # 应用基本信息
  application:
    name: meals

  # 安全配置
  security:
    user:
      # 禁用默认用户
      name: disabled
      password: disabled
      roles: disabled
  
  # 数据源配置
  datasource:
    # MySQL 数据库配置
    url: ******************************************************************************************************************
    username: root
    password: "021026"
    driver-class-name: com.mysql.cj.jdbc.Driver

  # 文件上传配置
  servlet:
    multipart:
      enabled: true
      max-file-size: 10MB
      max-request-size: 20MB

    # 连接池配置
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: MealsHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000

  # 邮件配置
  mail:
    # QQ邮箱SMTP配置
    host: smtp.qq.com
    port: 587
    username: <EMAIL>
    # 需要配置QQ邮箱的授权码，不是登录密码
    password: msrmsjbvaizeecig 
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
          connectiontimeout: 5000
          timeout: 3000
          writetimeout: 5000
    default-encoding: UTF-8

# 文件上传配置
file:
  upload:
    path: uploads/
    avatar-path: uploads/avatars/
    allowed-types: jpg,jpeg,png,gif
    max-size: 10485760  # 10MB
    avatar-max-width: 500
    avatar-max-height: 500
    thumbnail-width: 100
    thumbnail-height: 100

# MyBatis 配置
mybatis:
  # Mapper XML 文件位置
  mapper-locations: classpath:mapper/*.xml
  # 实体类别名包路径
  type-aliases-package: com.example.meals.entity
  configuration:
    # 开启驼峰命名转换
    map-underscore-to-camel-case: true
    # 开启延迟加载
    lazy-loading-enabled: true
    # 开启二级缓存
    cache-enabled: true
    # 日志实现
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# 服务器配置
server:
  port: 8080
  servlet:
    encoding:
      charset: UTF-8
      enabled: true
      force: true

# JWT 配置
jwt:
  # JWT 密钥（生产环境应使用更复杂的密钥）
  secret: meals-jwt-secret-key-2024-very-long-and-secure-key-for-production
  # Token 过期时间（单位：毫秒，1天）
  expiration: 86400000
  # 记住我Token过期时间（单位：毫秒，30天）
  remember-me-expiration: 2592000000
  # Token 前缀
  token-prefix: "Bearer "
  # Token 请求头名称
  header-name: "Authorization"