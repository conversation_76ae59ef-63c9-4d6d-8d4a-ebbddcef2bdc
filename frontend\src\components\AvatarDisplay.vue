<template>
  <div class="avatar-display" :class="{ clickable: clickable }" @click="handleClick">
    <div class="avatar-container" :style="{ width: size + 'px', height: size + 'px' }">
      <!-- 自定义头像 -->
      <img 
        v-if="avatarUrl" 
        :src="avatarUrl" 
        :alt="name || 'Avatar'"
        class="avatar-image"
        @error="handleImageError"
      />
      <!-- 字母头像 -->
      <div 
        v-else 
        class="avatar-letter" 
        :style="{ 
          backgroundColor: letterAvatarColor,
          fontSize: letterFontSize + 'px',
          lineHeight: size + 'px'
        }"
      >
        {{ avatarLetter }}
      </div>
      
      <!-- 上传按钮覆盖层 -->
      <div v-if="showUploadOverlay" class="upload-overlay">
        <i class="upload-icon">📷</i>
      </div>
    </div>
    
    <!-- 状态指示器 -->
    <div v-if="showStatus" class="status-indicator" :class="statusClass"></div>
  </div>
</template>

<script>
export default {
  name: 'AvatarDisplay',
  props: {
    // 头像URL
    avatarUrl: {
      type: String,
      default: null
    },
    // 用户名或真实姓名
    name: {
      type: String,
      default: ''
    },
    // 头像尺寸
    size: {
      type: Number,
      default: 40
    },
    // 是否可点击
    clickable: {
      type: Boolean,
      default: false
    },
    // 是否显示上传覆盖层
    showUploadOverlay: {
      type: Boolean,
      default: false
    },
    // 是否显示状态指示器
    showStatus: {
      type: Boolean,
      default: false
    },
    // 状态类型 (online, offline, busy)
    status: {
      type: String,
      default: 'offline'
    }
  },
  computed: {
    // 字母头像显示的字符
    avatarLetter() {
      if (!this.name) return '?'
      
      // 如果是中文名，取最后一个字符
      if (/[\u4e00-\u9fa5]/.test(this.name)) {
        return this.name.charAt(this.name.length - 1)
      }
      
      // 如果是英文名，取第一个字符
      return this.name.charAt(0).toUpperCase()
    },
    
    // 字母头像背景色
    letterAvatarColor() {
      const colors = [
        '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
        '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
      ]
      
      if (!this.name) return colors[0]
      
      // 根据名字生成固定的颜色
      let hash = 0
      for (let i = 0; i < this.name.length; i++) {
        hash = this.name.charCodeAt(i) + ((hash << 5) - hash)
      }
      
      return colors[Math.abs(hash) % colors.length]
    },
    
    // 字母字体大小
    letterFontSize() {
      return Math.floor(this.size * 0.4)
    },
    
    // 状态指示器样式类
    statusClass() {
      return `status-${this.status}`
    }
  },
  methods: {
    handleClick(event) {
      if (this.clickable) {
        if (event && event.stopPropagation) {
          event.stopPropagation()
        }
        this.$emit('click')
      }
    },
    
    handleImageError() {
      // 图片加载失败时的处理
      this.$emit('image-error')
    }
  }
}
</script>

<style scoped>
.avatar-display {
  position: relative;
  display: inline-block;
}

.avatar-display.clickable {
  cursor: pointer;
}

.avatar-container {
  position: relative;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid #e1e5e9;
  transition: all 0.3s ease;
}

.avatar-display.clickable .avatar-container:hover {
  border-color: #007bff;
  transform: scale(1.05);
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.avatar-letter {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  text-align: center;
}

.upload-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.avatar-display.clickable:hover .upload-overlay {
  opacity: 1;
}

.upload-icon {
  font-size: 20px;
  color: white;
}

.status-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid white;
}

.status-online {
  background-color: #28a745;
}

.status-offline {
  background-color: #6c757d;
}

.status-busy {
  background-color: #dc3545;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .upload-overlay {
    opacity: 1;
  }
}
</style>
