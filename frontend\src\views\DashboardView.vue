<template>
  <div class="dashboard-container">
    <!-- 顶部导航栏 -->
    <nav class="top-navbar">
      <div class="nav-content">
        <div class="nav-brand">
          <div class="brand-logo">
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" 
                    fill="currentColor"/>
              <circle cx="12" cy="12" r="3" fill="none" stroke="currentColor" stroke-width="2"/>
            </svg>
          </div>
          <span class="brand-name">膳食营养分析平台</span>
        </div>
        <div class="nav-actions">
          <router-link to="/" class="nav-btn home-btn">首页</router-link>
          <span class="welcome-text">欢迎，{{ user?.username }}</span>
          <button @click="handleLogout" class="nav-btn logout-btn">退出登录</button>
        </div>
      </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="dashboard-main">
      <!-- 欢迎区域 -->
      <section class="welcome-section">
        <div class="welcome-content">
          <div class="user-profile">
            <div @click="triggerAvatarUpload" style="cursor: pointer;">
              <AvatarDisplay
                :avatar-url="getUserAvatarUrl(user?.avatar)"
                :name="user?.username"
                :size="80"
                :clickable="false"
                :show-upload-overlay="true"
              />
            </div>
            <div class="user-info">
              <h1 class="welcome-title">欢迎回来，{{ user?.username }}！</h1>
              <p class="welcome-subtitle">开始您的健康饮食管理之旅</p>
            </div>
          </div>
        </div>

        <!-- 隐藏的头像上传文件输入 -->
        <input
          ref="avatarFileInput"
          type="file"
          accept="image/jpeg,image/jpg,image/png,image/gif"
          style="display: none"
          @change="handleAvatarFileSelect"
        />
      </section>

      <!-- 功能卡片区域 -->
      <section class="features-section">
        <h2 class="section-title">功能中心</h2>
        <div class="features-grid">
          <div class="feature-card" @click="navigateToFeature('nutrition')">
            <div class="feature-icon">🥗</div>
            <h3 class="feature-title">营养分析</h3>
            <p class="feature-description">分析食物营养成分，制定健康饮食计划</p>
            <div class="feature-action">
              <span class="action-text">立即使用</span>
              <svg class="action-arrow" viewBox="0 0 24 24" fill="currentColor">
                <path d="M8.59 16.59L13.17 12L8.59 7.41L10 6L16 12L10 18L8.59 16.59Z"/>
              </svg>
            </div>
          </div>

          <div class="feature-card" @click="navigateToFeature('meal-record')">
            <div class="feature-icon">📝</div>
            <h3 class="feature-title">饮食记录</h3>
            <p class="feature-description">记录每日饮食，追踪营养摄入情况</p>
            <div class="feature-action">
              <span class="action-text">立即使用</span>
              <svg class="action-arrow" viewBox="0 0 24 24" fill="currentColor">
                <path d="M8.59 16.59L13.17 12L8.59 7.41L10 6L16 12L10 18L8.59 16.59Z"/>
              </svg>
            </div>
          </div>

          <div class="feature-card" @click="navigateToFeature('health-goals')">
            <div class="feature-icon">🎯</div>
            <h3 class="feature-title">健康目标</h3>
            <p class="feature-description">设置个人健康目标，监控达成进度</p>
            <div class="feature-action">
              <span class="action-text">立即使用</span>
              <svg class="action-arrow" viewBox="0 0 24 24" fill="currentColor">
                <path d="M8.59 16.59L13.17 12L8.59 7.41L10 6L16 12L10 18L8.59 16.59Z"/>
              </svg>
            </div>
          </div>

          <div class="feature-card" @click="navigateToFeature('reports')">
            <div class="feature-icon">📊</div>
            <h3 class="feature-title">健康报告</h3>
            <p class="feature-description">生成详细的营养分析和健康评估报告</p>
            <div class="feature-action">
              <span class="action-text">立即使用</span>
              <svg class="action-arrow" viewBox="0 0 24 24" fill="currentColor">
                <path d="M8.59 16.59L13.17 12L8.59 7.41L10 6L16 12L10 18L8.59 16.59Z"/>
              </svg>
            </div>
          </div>
        </div>
      </section>


    </main>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuth } from '../composables/useAuth'
import { getAvatarUrl as getUserAvatarUrl } from '../utils/userApi'
import AvatarDisplay from '../components/AvatarDisplay.vue'
import { message } from '../utils/message'

const router = useRouter()
const { user, logout, updateUserState } = useAuth()

// 处理登出
const handleLogout = () => {
  logout()
  updateUserState()
  router.push('/')
}

// 导航到功能页面
const navigateToFeature = (feature: string) => {
  // 这里可以根据功能类型导航到不同的页面
  // 暂时显示提示，后续可以添加具体的功能页面
  // 功能开发中，暂时不执行任何操作
}

// 头像上传相关方法
const avatarFileInput = ref<HTMLInputElement>()
const isUploading = ref(false)

const triggerAvatarUpload = () => {
  // 防止重复触发
  if (isUploading.value) {
    return
  }

  // 触发文件选择
  if (avatarFileInput.value) {
    avatarFileInput.value.click()
  }
}

const handleAvatarFileSelect = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  // 如果没有选择文件或正在上传，直接返回
  if (!file || isUploading.value) return

  // 验证文件
  if (!validateAvatarFile(file)) {
    return
  }

  isUploading.value = true

  try {
    // 上传头像
    const formData = new FormData()
    formData.append('file', file)

    const response = await fetch('http://localhost:8080/api/files/avatar/user', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: formData
    })

    const result = await response.json()

    if (result.success) {
      // 更新头像路径
      if (user.value) {
        user.value.avatar = result.data
      }
      message.success('头像上传成功')
    } else {
      message.error(result.message || '头像上传失败')
    }
  } catch (error) {
    message.error('头像上传失败，请重试')
  } finally {
    isUploading.value = false
    // 清空文件输入
    target.value = ''
  }
}

const validateAvatarFile = (file: File): boolean => {
  // 检查文件类型
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']
  if (!allowedTypes.includes(file.type)) {
    message.error('只支持 JPG、PNG、GIF 格式的图片')
    return false
  }

  // 检查文件大小 (10MB)
  const maxSize = 10 * 1024 * 1024
  if (file.size > maxSize) {
    message.error('文件大小不能超过 10MB')
    return false
  }

  return true
}

onMounted(() => {
  // 路由守卫已经验证了用户权限，这里不需要重复检查
  // 可以在这里执行其他初始化逻辑
})
</script>

<style scoped>
.dashboard-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

/* 顶部导航栏 */
.top-navbar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding: 1rem 0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.brand-logo {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #16a085, #1abc9c);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.brand-logo svg {
  width: 24px;
  height: 24px;
}

.brand-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2d3748;
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.welcome-text {
  color: #4a5568;
  font-size: 0.9rem;
}

.nav-btn {
  padding: 0.5rem 1rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  font-size: 0.9rem;
}

.home-btn {
  background: #e2e8f0;
  color: #4a5568;
}

.home-btn:hover {
  background: #cbd5e0;
}

.logout-btn {
  background: #fed7d7;
  color: #c53030;
}

.logout-btn:hover {
  background: #feb2b2;
}

/* 主要内容区域 */
.dashboard-main {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

/* 欢迎区域 */
.welcome-section {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.welcome-content {
  display: flex;
  justify-content: center;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 20px;
  text-align: left;
}

.user-info {
  flex: 1;
}

.welcome-title {
  font-size: 2rem;
  font-weight: 700;
  color: #2d3748;
  margin: 0 0 0.5rem 0;
}

.welcome-subtitle {
  color: #718096;
  font-size: 1.1rem;
  margin: 0;
}



/* 功能卡片区域 */
.features-section {
  margin-bottom: 2rem;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 1.5rem 0;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.feature-card {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #16a085;
}

.feature-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.feature-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 0.5rem 0;
}

.feature-description {
  color: #718096;
  line-height: 1.5;
  margin: 0 0 1rem 0;
}

.feature-action {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #16a085;
  font-weight: 500;
}

.action-arrow {
  width: 20px;
  height: 20px;
  transition: transform 0.3s ease;
}

.feature-card:hover .action-arrow {
  transform: translateX(4px);
}



/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard-main {
    padding: 1rem;
  }
  
  .welcome-section {
    padding: 1.5rem;
  }

  .user-profile {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }

  .welcome-title {
    font-size: 1.5rem;
  }
  
  .nav-content {
    padding: 0 1rem;
  }
  
  .brand-name {
    display: none;
  }
}
</style>
